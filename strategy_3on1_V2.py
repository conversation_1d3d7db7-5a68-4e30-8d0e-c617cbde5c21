# 2024/08/01  止损卖出修改为跌破5日均线
# 合并四大JSG策略作为第四个子策略

from jqdata import *
from jqfactor import *
from jqlib.technical_analysis import *
import datetime as dt
import pandas as pd
import numpy as np
from datetime import datetime
from datetime import timedelta
import math
import pickle
from six import StringIO,BytesIO # py3的环境，使用BytesIO
import talib


# JSG策略相关常量
SW1 = {
    '801010': '农林牧渔I',
    '801020': '采掘I',
    '801030': '化工I',
    '801040': '钢铁I',
    '801050': '有色金属I',
    '801060': '建筑建材I',
    '801070': '机械设备I',
    '801080': '电子I',
    '801090': '交运设备I',
    '801100': '信息设备I',
    '801110': '家用电器I',
    '801120': '食品饮料I',
    '801130': '纺织服装I',
    '801140': '轻工制造I',
    '801150': '医药生物I',
    '801160': '公用事业I',
    '801170': '交通运输I',
    '801180': '房地产I',
    '801190': '金融服务I',
    '801200': '商业贸易I',
    '801210': '休闲服务I',
    '801220': '信息服务I',
    '801230': '综合I',
    '801710': '建筑材料I',
    '801720': '建筑装饰I',
    '801730': '电气设备I',
    '801740': '国防军工I',
    '801750': '计算机I',
    '801760': '传媒I',
    '801770': '通信I',
    '801780': '银行I',
    '801790': '非银金融I',
    '801880': '汽车I',
    '801890': '机械设备I',
    '801950': '煤炭I',
    '801960': '石油石化I',
    '801970': '环保I',
    '801980': '美容护理I'
}

# JSG策略行业代码
industry_code = ['801010','801020','801030','801040','801050','801080','801110','801120','801130','801140','801150',\
                    '801160','801170','801180','801200','801210','801230','801710','801720','801730','801740','801750',\
                   '801760','801770','801780','801790','801880','801890']

def initialize(context):
    set_option('use_real_price', True)
    log.set_level('system', 'error')
    set_option('avoid_future_data', True)

    # JSG策略全局变量
    g.jsg_stock_num = 10
    g.jsg_hold_list = []  # JSG策略当前持仓的全部股票
    g.jsg_yesterday_HL_list = []  # JSG策略记录持仓中昨日涨停的股票
    g.jsg_num = 1

    # 一进二
    run_daily(get_stock_list, '09:01')
    run_daily(buy, '09:26')
    run_daily(sell, time='11:25', reference_security='000300.XSHG')
    run_daily(sell, time='14:50', reference_security='000300.XSHG')

    # JSG策略调度
    run_daily(prepare_jsg_stock_list, '9:05')
    run_weekly(weekly_jsg_adjustment, 1, '9:30')
    run_daily(check_jsg_limit_up, '14:00')  # 检查JSG持仓中的涨停股是否需要卖出

    # 首版低开
    # run_daily(buy2, '09:27') #9:25分知道开盘价后可以提前下单


# 选股
def get_stock_list(context):
    # 文本日期
    date = context.previous_date
    date = transform_date(date, 'str')
    date_1=get_shifted_date(date, -1, 'T')
    date_2=get_shifted_date(date, -2, 'T')

    # 初始列表
    initial_list = prepare_stock_list(date)
    # 昨日涨停
    hl_list = get_hl_stock(initial_list, date)
    # 前日曾涨停
    hl1_list = get_ever_hl_stock(initial_list, date_1)
    # 前前日曾涨停
    hl2_list = get_ever_hl_stock(initial_list, date_2)
    # 合并 hl1_list 和 hl2_list 为一个集合，用于快速查找需要剔除的元素
    elements_to_remove = set(hl1_list + hl2_list)
    # 使用列表推导式来剔除 hl_list 中存在于 elements_to_remove 集合中的元素
    hl_list = [stock for stock in hl_list if stock not in elements_to_remove]

    g.target_list = hl_list

    # 昨日曾涨停
    h1_list = get_ever_hl_stock2(initial_list, date)
    # 上上个交易日涨停过滤
    elements_to_remove = get_hl_stock(initial_list, date_1)

    # 过滤上上个交易日涨停、曾涨停
    all_list = [stock for stock in h1_list if stock not in elements_to_remove]

    g.target_list2 = all_list





# 交易
def buy(context):
    # 先进行择时判断
    print('开始进行择时判断...')
    timing_result = select_timing(context)
    print('择时判断结果:', timing_result)

    if not timing_result:
        print('今日择时信号不满足，不进行交易')
        return

    print('择时信号满足，开始选股...')
    qualified_stocks = []
    gk_stocks=[]
    dk_stocks=[]
    rzq_stocks=[]
    current_data = get_current_data()
    date_now = context.current_dt.strftime("%Y-%m-%d")
    mid_time1 = ' 09:15:00'
    end_times1 =  ' 09:26:00'
    start = date_now + mid_time1
    end = date_now + end_times1
    # 高开
    for s in g.target_list:
        # 条件一：均价，金额，市值，换手率
        prev_day_data = attribute_history(s, 1, '1d', fields=['close', 'volume', 'money'], skip_paused=True)
        avg_price_increase_value = prev_day_data['money'][0] / prev_day_data['volume'][0] / prev_day_data['close'][0] * 1.1 - 1
        if avg_price_increase_value < 0.07 or prev_day_data['money'][0] < 5.5e8 or prev_day_data['money'][0] > 20e8 :
            continue
        # market_cap 总市值(亿元) > 70亿 流通市值(亿元) < 520亿
        turnover_ratio_data=get_valuation(s, start_date=context.previous_date, end_date=context.previous_date, fields=['turnover_ratio', 'market_cap','circulating_market_cap'])
        if turnover_ratio_data.empty or turnover_ratio_data['market_cap'][0] < 70  or turnover_ratio_data['circulating_market_cap'][0] > 520 :
            continue
        # if turnover_ratio_data.empty or turnover_ratio_data['turnover_ratio'][0] < 5:
        #     continue

        # 条件二：左压
        zyts = calculate_zyts(s, context)
        volume_data = attribute_history(s, zyts, '1d', fields=['volume'], skip_paused=True)
        if len(volume_data) < 2 or volume_data['volume'][-1] <= max(volume_data['volume'][:-1]) * 0.9:
            continue

        # 条件三：高开,开比
        # log.info(s)
        auction_data = get_call_auction(s, start_date=start, end_date=end, fields=['time','volume', 'current'])
        # log.info(auction_data)
        if auction_data.empty or auction_data['volume'][0] / volume_data['volume'][-1] < 0.03:
            continue
        current_ratio = auction_data['current'][0] / (current_data[s].high_limit/1.1)
        if current_ratio<=1 or current_ratio>=1.06:
            continue

        # 如果股票满足所有条件，则添加到列表中
        gk_stocks.append(s)
        qualified_stocks.append(s)


    # 低开
    # 基础信息
    date = transform_date(context.previous_date, 'str')
    current_data = get_current_data()

    # 昨日涨停列表
    initial_list = prepare_stock_list2(date)
    hl_list = get_hl_stock(initial_list, date)

    if len(hl_list) != 0:
        # 获取非连板涨停的股票
        ccd = get_continue_count_df(hl_list, date, 10)
        lb_list = list(ccd.index)
        stock_list = [s for s in hl_list if s not in lb_list]

        # 计算相对位置
        rpd = get_relative_position_df(stock_list, date, 60)
        rpd = rpd[rpd['rp'] <= 0.5]
        stock_list = list(rpd.index)

        # 低开
        df =  get_price(stock_list, end_date=date, frequency='daily', fields=['close'], count=1, panel=False, fill_paused=False, skip_paused=True).set_index('code') if len(stock_list) != 0 else pd.DataFrame()
        df['open_pct'] = [current_data[s].day_open/df.loc[s, 'close'] for s in stock_list]
        df = df[(0.955 <= df['open_pct']) & (df['open_pct'] <= 0.97)] #低开越多风险越大，选择3个多点即可
        stock_list = list(df.index)
        # send_message(','.join(stock_list))
        # print(df)

        for s in stock_list:
            prev_day_data = attribute_history(s, 1, '1d', fields=['close', 'volume', 'money'], skip_paused=True)
            if prev_day_data['money'][0] >= 1e8  :
                dk_stocks.append(s)
                qualified_stocks.append(s)

    # 弱转强
    for s in g.target_list2:
        # 过滤前面三天涨幅超过28%的票
        price_data = attribute_history(s, 4, '1d', fields=['close'], skip_paused=True)
        if len(price_data) < 4:
            continue
        increase_ratio = (price_data['close'][-1] - price_data['close'][0]) / price_data['close'][0]
        if increase_ratio > 0.28:
            continue

        # 过滤前一日收盘价小于开盘价5%以上的票
        prev_day_data = attribute_history(s, 1, '1d', fields=['open', 'close'], skip_paused=True)
        if len(prev_day_data) < 1:
            continue
        open_close_ratio = (prev_day_data['close'][0] - prev_day_data['open'][0]) / prev_day_data['open'][0]
        if open_close_ratio < -0.05:
            continue

        prev_day_data = attribute_history(s, 1, '1d', fields=['close', 'volume','money'], skip_paused=True)
        avg_price_increase_value = prev_day_data['money'][0] / prev_day_data['volume'][0] / prev_day_data['close'][0]  - 1
        if avg_price_increase_value < -0.04 or prev_day_data['money'][0] < 3e8 or prev_day_data['money'][0] > 19e8:
            continue
        turnover_ratio_data = get_valuation(s, start_date=context.previous_date, end_date=context.previous_date, fields=['turnover_ratio','market_cap','circulating_market_cap'])
        if turnover_ratio_data.empty or turnover_ratio_data['market_cap'][0] < 70  or turnover_ratio_data['circulating_market_cap'][0] > 520 :
            continue

        zyts = calculate_zyts(s, context)
        volume_data = attribute_history(s, zyts, '1d', fields=['volume'], skip_paused=True)
        if len(volume_data) < 2 or volume_data['volume'][-1] <= max(volume_data['volume'][:-1]) * 0.9:
            continue

        auction_data = get_call_auction(s, start_date=start, end_date=end, fields=['time','volume', 'current'])

        if auction_data.empty or auction_data['volume'][0] / volume_data['volume'][-1] < 0.03:
            continue
        current_ratio = auction_data['current'][0] / (current_data[s].high_limit/1.1)
        if current_ratio <= 0.98 or current_ratio >= 1.09:
            continue
        rzq_stocks.append(s)
        qualified_stocks.append(s)

    # JSG策略选股
    jsg_stocks = []
    jsg_target_stocks = get_jsg_stock_list(context)
    for s in jsg_target_stocks:
        jsg_stocks.append(s)
        qualified_stocks.append(s)

    if len(qualified_stocks)>0:
        print('———————————————————————————————————')
        send_message('今日选股：'+','.join(qualified_stocks))
        print('一进二：'+','.join(gk_stocks))
        print('首板低开：'+','.join(dk_stocks))
        print('弱转强：'+','.join(rzq_stocks))
        print('JSG策略：'+','.join(jsg_stocks))
        print('今日选股：'+','.join(qualified_stocks))
        print('———————————————————————————————————')
    else:
        send_message('今日无目标个股')
        print('今日无目标个股')


    if len(qualified_stocks)!=0  and context.portfolio.available_cash/context.portfolio.total_value>0.3:
        value = context.portfolio.available_cash / len(qualified_stocks)
        for s in qualified_stocks:
            # 下单
            #由于关闭了错误日志，不加这一句，不足一手买入失败也会打印买入，造成日志不准确
            if context.portfolio.available_cash/current_data[s].last_price>100:
                order_value(s, value, MarketOrderStyle(current_data[s].day_open))
                print('买入' + s)
                print('———————————————————————————————————')

# 处理日期相关函数
def transform_date(date, date_type):
    if type(date) == str:
        str_date = date
        dt_date = dt.datetime.strptime(date, '%Y-%m-%d')
        d_date = dt_date.date()
    elif type(date) == dt.datetime:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = date
        d_date = dt_date.date()
    elif type(date) == dt.date:
        str_date = date.strftime('%Y-%m-%d')
        dt_date = dt.datetime.strptime(str_date, '%Y-%m-%d')
        d_date = date
    dct = {'str':str_date, 'dt':dt_date, 'd':d_date}
    return dct[date_type]

def get_shifted_date(date, days, days_type='T'):
    #获取上一个自然日
    d_date = transform_date(date, 'd')
    yesterday = d_date + dt.timedelta(-1)
    #移动days个自然日
    if days_type == 'N':
        shifted_date = yesterday + dt.timedelta(days+1)
    #移动days个交易日
    if days_type == 'T':
        all_trade_days = [i.strftime('%Y-%m-%d') for i in list(get_all_trade_days())]
        #如果上一个自然日是交易日，根据其在交易日列表中的index计算平移后的交易日
        if str(yesterday) in all_trade_days:
            shifted_date = all_trade_days[all_trade_days.index(str(yesterday)) + days + 1]
        #否则，从上一个自然日向前数，先找到最近一个交易日，再开始平移
        else:
            for i in range(100):
                last_trade_date = yesterday - dt.timedelta(i)
                if str(last_trade_date) in all_trade_days:
                    shifted_date = all_trade_days[all_trade_days.index(str(last_trade_date)) + days + 1]
                    break
    return str(shifted_date)



# 过滤函数
def filter_new_stock(initial_list, date, days=50):
    d_date = transform_date(date, 'd')
    return [stock for stock in initial_list if d_date - get_security_info(stock).start_date > dt.timedelta(days=days)]



def filter_st_stock(initial_list, date):
    str_date = transform_date(date, 'str')
    if get_shifted_date(str_date, 0, 'N') != get_shifted_date(str_date, 0, 'T'):
        str_date = get_shifted_date(str_date, -1, 'T')
    df = get_extras('is_st', initial_list, start_date=str_date, end_date=str_date, df=True)
    df = df.T
    df.columns = ['is_st']
    df = df[df['is_st'] == False]
    filter_list = list(df.index)
    return filter_list

def filter_kcbj_stock(initial_list):
    return [stock for stock in initial_list
    if stock[0] != '4'
    and stock[0] != '8'
    # and stock[0] != '3'
    and stock[:2] != '68']

def filter_paused_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['paused'], count=1, panel=False, fill_paused=True)
    df = df[df['paused'] == 0]
    paused_list = list(df.code)
    return paused_list

# 一字
def filter_extreme_limit_stock(context, stock_list, date):
    tmp = []
    for stock in stock_list:
        df = get_price(stock, end_date=date, frequency='daily', fields=['low','high_limit'], count=1, panel=False)
        if df.iloc[0,0] < df.iloc[0,1]:
            tmp.append(stock)
    return tmp



# 每日初始股票池
def prepare_stock_list(date):
    initial_list = get_all_securities('stock', date).index.tolist()
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list


# 计算左压天数
def calculate_zyts(s, context):
    high_prices = attribute_history(s, 101, '1d', fields=['high'], skip_paused=True)['high']
    prev_high = high_prices.iloc[-1]
    zyts_0 = next((i-1 for i, high in enumerate(high_prices[-3::-1], 2) if high >= prev_high), 100)
    zyts = zyts_0 + 5
    return zyts


# 筛选出某一日涨停的股票
def get_hl_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['close','high_limit'], count=1, panel=False, fill_paused=False, skip_paused=False)
    df = df.dropna() #去除停牌
    df = df[df['close'] == df['high_limit']]
    hl_list = list(df.code)
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['high','high_limit'], count=1, panel=False, fill_paused=False, skip_paused=False)
    df = df.dropna() #去除停牌
    df = df[df['high'] == df['high_limit']]
    hl_list = list(df.code)
    return hl_list

# 筛选曾涨停
def get_ever_hl_stock2(initial_list, date):
    df = get_price(initial_list, end_date=date, frequency='daily', fields=['close','high','high_limit'], count=1, panel=False, fill_paused=False, skip_paused=False)
    df = df.dropna() #去除停牌
    cd1 = df['high'] == df['high_limit']
    cd2 = df['close']!= df['high_limit']
    df = df[cd1 & cd2]
    hl_list = list(df.code)
    return hl_list

# 计算涨停数
def get_hl_count_df(hl_list, date, watch_days):
    # 获取watch_days的数据
    df = get_price(hl_list, end_date=date, frequency='daily', fields=['close','high_limit','low'], count=watch_days, panel=False, fill_paused=False, skip_paused=False)
    df.index = df.code
    #计算涨停与一字涨停数，一字涨停定义为最低价等于涨停价
    hl_count_list = []
    extreme_hl_count_list = []
    for stock in hl_list:
        df_sub = df.loc[stock]
        hl_days = df_sub[df_sub.close==df_sub.high_limit].high_limit.count()
        extreme_hl_days = df_sub[df_sub.low==df_sub.high_limit].high_limit.count()
        hl_count_list.append(hl_days)
        extreme_hl_count_list.append(extreme_hl_days)
    #创建df记录
    df = pd.DataFrame(index=hl_list, data={'count':hl_count_list, 'extreme_count':extreme_hl_count_list})
    return df

# 计算连板数
def get_continue_count_df(hl_list, date, watch_days):
    df = pd.DataFrame()
    for d in range(2, watch_days+1):
        HLC = get_hl_count_df(hl_list, date, d)
        CHLC = HLC[HLC['count'] == d]
        df = df.append(CHLC)
    stock_list = list(set(df.index))
    ccd = pd.DataFrame()
    for s in stock_list:
        tmp = df.loc[[s]]
        if len(tmp) > 1:
            M = tmp['count'].max()
            tmp = tmp[tmp['count'] == M]
        ccd = ccd.append(tmp)
    if len(ccd) != 0:
        ccd = ccd.sort_values(by='count', ascending=False)
    return ccd

# 计算昨涨幅
def get_index_increase_ratio(index_code, context):
    # 获取指数昨天和前天的收盘价
    close_prices = attribute_history(index_code, 2, '1d', fields=['close'], skip_paused=True)
    if len(close_prices) < 2:
        return 0  # 如果数据不足，返回0
    day_before_yesterday_close = close_prices['close'][0]
    yesterday_close = close_prices['close'][1]

    # 计算涨幅
    increase_ratio = (yesterday_close - day_before_yesterday_close) / day_before_yesterday_close
    return increase_ratio

# 获取股票所属行业
def getStockIndustry(stocks):
    try:
        # 使用get_industry函数一次性获取多只股票的行业信息
        industry = get_industry(stocks)
        # 提取申万一级行业名称
        return pd.Series({stock: info["sw_l1"]["industry_name"] for stock, info in industry.items() if "sw_l1" in info})
    except Exception as e:
        print(f"获取行业信息出错: {str(e)}")
        return pd.Series()

# 获取市场宽度
def get_market_breadth(context):
    try:
        print("开始计算市场宽度...")
        # 指定日期防止未来数据
        yesterday = context.previous_date
        print(f"使用日期: {yesterday}")

        # 获取初始列表 - 使用中证全指
        stocks = get_index_stocks("000985.XSHG")
        print(f"获取到 {len(stocks)} 只股票")

        # 获取股票价格数据
        count = 1
        h = get_price(
            stocks,
            end_date=yesterday,
            frequency="1d",
            fields=["close"],
            count=count + 20,
            panel=False,
        )

        # 处理数据
        h["date"] = pd.DatetimeIndex(h.time).date
        df_close = h.pivot(index="code", columns="date", values="close").dropna(axis=0)
        print(f"有效数据股票数量: {len(df_close)}")

        # 计算20日均线
        df_ma20 = df_close.rolling(window=20, axis=1).mean().iloc[:, -count:]

        # 计算偏离程度（股价是否高于20日均线）
        df_bias = df_close.iloc[:, -count:] > df_ma20

        # 获取股票所属行业
        industry_series = getStockIndustry(stocks)
        print(f"获取到 {len(industry_series)} 只股票的行业信息")

        # 将行业信息添加到df_bias
        df_bias["industry_name"] = industry_series

        # 去除没有行业信息的股票
        df_bias = df_bias.dropna(subset=["industry_name"])
        print(f"有效行业信息股票数量: {len(df_bias)}")

        # 计算行业偏离比例
        df_ratio = ((df_bias.groupby("industry_name").sum() * 100.0) / df_bias.groupby("industry_name").count()).round()
        print(f"行业数量: {len(df_ratio)}")

        # 获取偏离程度最高的行业（仅获取Top1）
        top_value = df_ratio.loc[:, yesterday].nlargest(1)
        top_industry = top_value.index.tolist()[0] if len(top_value) > 0 else ""
        print("市场宽度计算结果 - 领先行业Top1:", top_industry)
        return top_industry
    except Exception as e:
        print(f"市场宽度计算失败: {str(e)}")
        # 出错时返回空字符串
        return ""

# 择时判断
def select_timing(context):
    print("开始执行择时判断函数...")
    try:
        # 获取市场宽度Top1行业
        top_industry = get_market_breadth(context)
        print("获取到的市场宽度领先行业Top1:", top_industry)

        # 需要监控的行业
        restricted_industries = ["银行", "有色金属", "钢铁", "煤炭"]
        print("需要监控的行业:", restricted_industries)

        # 检查Top1行业是否在需要监控的行业中
        is_restricted = False
        for industry in restricted_industries:
            if industry in top_industry:
                is_restricted = True
                print(f"Top1行业 '{top_industry}' 包含监控行业 '{industry}'")
                break

        if not is_restricted:
            print("Top1行业不在监控行业中，择时条件满足，可以交易")
            return True
        else:
            print("Top1行业在监控行业中，择时条件不满足，不进行交易")
            return False
    except Exception as e:
        print("择时判断出错:", str(e))
        # 出错时默认允许交易
        return True

#上午有利润就跑
def sell(context):
    # 基础信息
    date = transform_date(context.previous_date, 'str')
    current_data = get_current_data()


    # 根据时间执行不同的卖出策略
    if str(context.current_dt)[-8:] == '11:25:00' :
        for s in list(context.portfolio.positions):
            if ((context.portfolio.positions[s].closeable_amount != 0) and (current_data[s].last_price < current_data[s].high_limit) and (current_data[s].last_price > 1*context.portfolio.positions[s].avg_cost)):#avg_cost当前持仓成本
                order_target_value(s, 0)
                print( '止盈卖出', [s,get_security_info(s, date).display_name])
                print('———————————————————————————————————')

    if str(context.current_dt)[-8:] == '14:50:00':
        for s in list(context.portfolio.positions):
            # close_data = attribute_history(s, 5, '1d', ['close'])
            #     # 取得过去五天的平均价格
            # MA5 = close_data['close'].mean()
            # print(MA5)
            close_data2 = attribute_history(s, 4, '1d', ['close'])
            M4=close_data2['close'].mean()
            MA5=(M4*4+current_data[s].last_price)/5
            # print(current_data[s].last_price)
            # if ((context.portfolio.positions[s].closeable_amount != 0) and (current_data[s].last_price < current_data[s].high_limit)):
            if ((context.portfolio.positions[s].closeable_amount != 0) and (current_data[s].last_price < current_data[s].high_limit) and (current_data[s].last_price > 1*context.portfolio.positions[s].avg_cost)):#avg_cost当前持仓成本
                order_target_value(s, 0)
                print( '止盈卖出', [s,get_security_info(s, date).display_name])
                print('———————————————————————————————————')
            elif ((context.portfolio.positions[s].closeable_amount != 0) and (current_data[s].last_price < MA5)):
                #closeable_amount可卖出的仓位
                order_target_value(s, 0)
                print( '止损卖出', [s,get_security_info(s, date).display_name])
                print('———————————————————————————————————')


# 首版低开策略代码
def filter_new_stock2(initial_list, date, days=250):
    d_date = transform_date(date, 'd')
    return [stock for stock in initial_list if d_date - get_security_info(stock).start_date > dt.timedelta(days=days)]


# 每日初始股票池
def prepare_stock_list2(date):
    initial_list = get_all_securities('stock', date).index.tolist()
    initial_list = filter_kcbj_stock(initial_list)
    initial_list = filter_new_stock2(initial_list, date)
    initial_list = filter_st_stock(initial_list, date)
    initial_list = filter_paused_stock(initial_list, date)
    return initial_list

# 计算股票处于一段时间内相对位置
def get_relative_position_df(stock_list, date, watch_days):
    if len(stock_list) != 0:
        df = get_price(stock_list, end_date=date, fields=['high', 'low', 'close'], count=watch_days, fill_paused=False, skip_paused=False, panel=False).dropna()
        close = df.groupby('code').apply(lambda df: df.iloc[-1,-1])
        high = df.groupby('code').apply(lambda df: df['high'].max())
        low = df.groupby('code').apply(lambda df: df['low'].min())
        result = pd.DataFrame()
        result['rp'] = (close-low) / (high-low)
        return result
    else:
        return pd.DataFrame(columns=['rp'])


# ==================== JSG策略相关函数 ====================

# JSG策略 - 准备股票池
def prepare_jsg_stock_list(context):
    # 获取已持有列表
    g.jsg_hold_list = []
    for position in list(context.portfolio.positions.values()):
        stock = position.security
        # 只统计JSG策略的持仓（这里简化处理，实际可以通过标记区分）
        g.jsg_hold_list.append(stock)

    # 获取昨日涨停列表
    if g.jsg_hold_list != []:
        df = get_price(g.jsg_hold_list, end_date=context.previous_date, frequency='daily', fields=['close', 'high_limit'],
                       count=1, panel=False, fill_paused=False)
        df = df[df['close'] == df['high_limit']]
        g.jsg_yesterday_HL_list = list(df.code)
    else:
        g.jsg_yesterday_HL_list = []

# JSG策略 - 获取股票行业信息
def getStockIndustry_jsg(p_stocks, p_industries_type, p_day):
    dict_stk_2_ind = {}
    stocks_industry_dict = get_industry(p_stocks, date=p_day)
    for stock in stocks_industry_dict:
        if p_industries_type in stocks_industry_dict[stock]:
            dict_stk_2_ind[stock] = stocks_industry_dict[stock][p_industries_type]['industry_code']
    return pd.Series(dict_stk_2_ind)

# JSG策略 - 选股模块
def get_jsg_stock_list(context):
    # 指定日期防止未来数据
    yesterday = context.previous_date
    today = context.current_dt
    final_list = []

    # 获取初始列表
    initial_list = get_index_stocks('000985.XSHG', today)
    p_count = 1
    p_industries_type = 'sw_l1'
    h = get_price(initial_list, end_date=yesterday, frequency='1d', fields=['close'], count=p_count + 20, panel=False)
    h['date'] = pd.DatetimeIndex(h.time).date
    df_close = h.pivot(index='code', columns='date', values='close').dropna(axis=0)
    df_ma20 = df_close.rolling(window=20, axis=1).mean().iloc[:, -p_count:]
    df_bias = (df_close.iloc[:, -p_count:] > df_ma20)
    s_stk_2_ind = getStockIndustry_jsg(p_stocks=initial_list, p_industries_type=p_industries_type, p_day=yesterday)
    df_bias['industry_code'] = s_stk_2_ind
    df_ratio = ((df_bias.groupby('industry_code').sum() * 100.0) / df_bias.groupby(
        'industry_code').count()).round()
    column_names = df_ratio.columns.tolist()
    top_values = df_ratio[datetime.date(yesterday.year, yesterday.month, yesterday.day)].nlargest(g.jsg_num)
    I = top_values.index.tolist()
    sum_of_top_values = df_ratio.sum()
    TT = sum_of_top_values[datetime.date(yesterday.year, yesterday.month, yesterday.day)]
    name_list = [SW1[code] for code in I]
    print("JSG策略市场宽度分析:", name_list)
    print('JSG策略全市场宽度：', np.array(df_ratio.sum(axis=0).mean()))

    if '801780' not in I and '801050' not in I and '801950' not in I and '801040' not in I:
        # 《银行、有色金属、钢铁、煤炭》搅屎棍不在，开仓
        S_stocks = get_index_stocks('399101.XSHE', today)
        stocks = filter_kcbj_stock_jsg(S_stocks)
        choice = filter_st_stock_jsg(stocks)
        choice = filter_new_stock_jsg(context, choice)
        BIG_stock_list = get_fundamentals(query(
                valuation.code,
            ).filter(
                valuation.code.in_(choice),
                indicator.roe > 0.15,
                indicator.roa > 0.10,
            ).order_by(
        valuation.market_cap.asc()).limit(g.jsg_stock_num)).set_index('code').index.tolist()
        BIG_stock_list = filter_paused_stock_jsg(BIG_stock_list)
        BIG_stock_list = filter_limitup_stock_jsg(context, BIG_stock_list)
        L = filter_limitdown_stock_jsg(context, BIG_stock_list)
    else:
        print('JSG策略：四大搅屎棍在榜，不开仓')
        L = []
    return L

# JSG策略 - 整体调整持仓
def weekly_jsg_adjustment(context):
    target_B = get_jsg_stock_list(context)
    # 调仓卖出
    for stock in g.jsg_hold_list:
        if (stock not in target_B) and (stock not in g.jsg_yesterday_HL_list):
            position = context.portfolio.positions[stock]
            close_jsg_position(position)
    position_count = len(context.portfolio.positions)
    target_num = len(target_B)
    if target_num > position_count:
        buy_num = min(len(target_B), g.jsg_stock_num*g.jsg_num - position_count)
        value = context.portfolio.cash / buy_num
        for stock in target_B:
            if stock not in list(context.portfolio.positions.keys()):
                if open_jsg_position(stock, value):
                    if len(context.portfolio.positions) == target_num:
                        break

def check_jsg_limit_up(context):
    now_time = context.current_dt
    if g.jsg_yesterday_HL_list != []:
        # 对昨日涨停股票观察到尾盘如不涨停则提前卖出，如果涨停即使不在应买入列表仍暂时持有
        for stock in g.jsg_yesterday_HL_list:
            current_data = get_price(stock, end_date=now_time, frequency='1m', fields=['close', 'high_limit'],
                                     skip_paused=False, fq='pre', count=1, panel=False, fill_paused=True)
            if current_data.iloc[0, 0] < current_data.iloc[0, 1]:
                log.info("[%s]涨停打开，卖出" % (stock))
                position = context.portfolio.positions[stock]
                close_jsg_position(position)
            else:
                log.info("[%s]涨停，继续持有" % (stock))

# JSG策略交易模块-自定义下单
def order_target_value_jsg(security, value):
    if value == 0:
        log.debug("Selling out %s" % (security))
    else:
        log.debug("Order %s to value %f" % (security, value))
    return order_target_value(security, value)

# JSG策略交易模块-开仓
def open_jsg_position(security, value):
    order = order_target_value_jsg(security, value)
    if order != None and order.filled > 0:
        return True
    return False

# JSG策略交易模块-平仓
def close_jsg_position(position):
    security = position.security
    order = order_target_value_jsg(security, 0)  # 可能会因停牌失败
    if order != None:
        if order.status == OrderStatus.held and order.filled == order.amount:
            return True
    return False

# JSG策略过滤函数
# 过滤停牌股票
def filter_paused_stock_jsg(stock_list):
    current_data = get_current_data()
    return [stock for stock in stock_list if not current_data[stock].paused]

# 过滤ST及其他具有退市标签的股票
def filter_st_stock_jsg(stock_list):
    current_data = get_current_data()
    return [stock for stock in stock_list
            if not current_data[stock].is_st
            and 'ST' not in current_data[stock].name
            and '*' not in current_data[stock].name
            and '退' not in current_data[stock].name]

# 过滤科创北交股票
def filter_kcbj_stock_jsg(stock_list):
    for stock in stock_list[:]:
        if stock[0] == '4' or stock[0] == '8' or stock[:2] == '68' or stock[0] == '3':
            stock_list.remove(stock)
    return stock_list

# 过滤涨停的股票
def filter_limitup_stock_jsg(context, stock_list):
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    current_data = get_current_data()
    return [stock for stock in stock_list if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] < current_data[stock].high_limit]

# 过滤跌停的股票
def filter_limitdown_stock_jsg(context, stock_list):
    last_prices = history(1, unit='1m', field='close', security_list=stock_list)
    current_data = get_current_data()
    return [stock for stock in stock_list if stock in context.portfolio.positions.keys()
            or last_prices[stock][-1] > current_data[stock].low_limit]

# 过滤次新股
def filter_new_stock_jsg(context, stock_list):
    yesterday = context.previous_date
    return [stock for stock in stock_list if
            not yesterday - get_security_info(stock).start_date < datetime.timedelta(days=375)]